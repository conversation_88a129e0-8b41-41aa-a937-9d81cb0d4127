[debug] [2025-07-29T10:55:58.119Z] ----------------------------------------------------------------------
[debug] [2025-07-29T10:55:58.122Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-29T10:55:58.123Z] CLI Version:   14.8.0
[debug] [2025-07-29T10:55:58.123Z] Platform:      win32
[debug] [2025-07-29T10:55:58.123Z] Node Version:  v22.14.0
[debug] [2025-07-29T10:55:58.124Z] Time:          Tue Jul 29 2025 22:55:58 GMT+1200 (New Zealand Standard Time)
[debug] [2025-07-29T10:55:58.124Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-29T10:55:58.459Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-29T10:55:58.460Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-29T10:55:58.472Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T10:55:58.473Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-29T10:55:58.479Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-kissandost-9570f.json
[debug] [2025-07-29T10:55:58.505Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T10:55:58.505Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T10:55:58.505Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T10:55:58.505Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-29T10:55:58.524Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json
[debug] [2025-07-29T10:55:58.527Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\malikeahtesham111_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-29T10:55:58.529Z] Checked if tokens are valid: false, expires at: 1753776549415
[debug] [2025-07-29T10:55:58.529Z] Checked if tokens are valid: false, expires at: 1753776549415
[debug] [2025-07-29T10:55:58.529Z] > refreshing access token with scopes: []
[debug] [2025-07-29T10:55:58.531Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-29T10:55:58.531Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-29T10:55:58.969Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-29T10:55:58.969Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-29T10:55:58.981Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig [none]
[debug] [2025-07-29T10:55:59.862Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig 200
[debug] [2025-07-29T10:55:59.863Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig {"projectId":"kissandost-9570f","databaseURL":"https://kissandost-9570f-default-rtdb.firebaseio.com","storageBucket":"kissandost-9570f.firebasestorage.app"}
[info] i  functions: Watching "E:\python_projects_factory\animal\mcp-server\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"E:\\python_projects_factory\\animal\\mcp-server\\functions\" for Cloud Functions..."}}
[debug] [2025-07-29T10:55:59.904Z] Validating nodejs source
[debug] [2025-07-29T10:56:01.763Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.1",
    "express": "^4.18.2",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-29T10:56:01.763Z] Building nodejs source
[debug] [2025-07-29T10:56:01.764Z] Failed to find version of module node: reached end of search path E:\python_projects_factory\animal\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-29T10:56:01.768Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-29T10:56:01.776Z] Found firebase-functions binary at 'E:\python_projects_factory\animal\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8275

[info] [dotenv@17.2.1] injecting env (2) from .env -- tip: ⚙️  override existing env vars with { override: true }

[debug] [2025-07-29T10:56:02.369Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[info] +  functions[australia-southeast1-animalApp]: http function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp)."}}
[debug] [2025-07-29T10:56:06.426Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-29T10:56:13.005Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T10:56:13.005Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T10:56:13.006Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T10:56:13.005Z"],"workRunningCount":1}
[debug] [2025-07-29T10:56:13.006Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T10:56:13.009Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T10:56:13.010Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-29T10:56:13.023Z] [worker-pool] addWorker(australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(australia-southeast1-animalApp)"}}
[debug] [2025-07-29T10:56:13.024Z] [worker-pool] Adding worker with key australia-southeast1-animalApp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key australia-southeast1-animalApp, total=1"}}
[debug] [2025-07-29T10:56:14.611Z] [runtime-status] [16596] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-29T10:56:14.612Z] [runtime-status] [16596] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T10:56:14.612Z] [runtime-status] [16596] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-29T10:56:14.613Z] [runtime-status] [16596] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T10:56:15.108Z] [runtime-status] [16596] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-29T10:56:15.108Z] [runtime-status] [16596] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-29T10:56:15.109Z] [runtime-status] [16596] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T10:56:15.116Z] [runtime-status] [16596] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-29T10:56:15.119Z] [runtime-status] [16596] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T10:56:15.119Z] [runtime-status] [16596] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[info] >  [dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] } {"user":"[dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m [dotenv@17.2.1] injecting env (0) from .env -- tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }"}}
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[debug] [2025-07-29T10:56:15.326Z] [runtime-status] [16596] Functions runtime initialized. {"cwd":"E:\\python_projects_factory\\animal\\mcp-server\\functions","node_version":"22.14.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Functions runtime initialized. {\"cwd\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\",\"node_version\":\"22.14.0\"}"}}
[debug] [2025-07-29T10:56:15.327Z] [runtime-status] [16596] Listening to port: \\?\pipe\fire_emu_825e052b48b0d5ba {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [16596] Listening to port: \\\\?\\pipe\\fire_emu_825e052b48b0d5ba"}}
[debug] [2025-07-29T10:56:15.416Z] [worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: IDLE"}}
[debug] [2025-07-29T10:56:15.416Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T10:56:15.417Z] [worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: BUSY"}}
[debug] [2025-07-29T10:56:15.422Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 4.9706ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 4.9706ms"}}
[debug] [2025-07-29T10:56:15.423Z] [worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: IDLE"}}
[debug] [2025-07-29T10:56:15.423Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T10:56:15.423Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T10:56:15.424Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T10:56:15.425Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T10:56:15.425Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T10:56:15.425Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T10:56:15.425Z"],"workRunningCount":1}
[debug] [2025-07-29T10:56:15.425Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T10:56:15.426Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T10:56:15.427Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T10:56:15.427Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T10:56:15.427Z] [worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: BUSY"}}
[info] >  🔁 Initializing routes... {"user":"🔁 Initializing routes...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Initializing routes..."}}
[info] >  OpenAI client initialized successfully {"user":"OpenAI client initialized successfully","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m OpenAI client initialized successfully"}}
[warn] !  functions: The Cloud Firestore emulator is not running, so calls to Firestore will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Cloud Firestore emulator is not running, so calls to Firestore will affect production."}}
[warn] !  functions: The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production."}}
[info] >  🔁 Routes Initialized Successfully... {"user":"🔁 Routes Initialized Successfully...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Routes Initialized Successfully..."}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    prompt: 'vY0bV80sl4vggj73KZwh', {"user":"  prompt: 'vY0bV80sl4vggj73KZwh',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'vY0bV80sl4vggj73KZwh',"}}
[info] >    selectedTaskId: 'vY0bV80sl4vggj73KZwh', {"user":"  selectedTaskId: 'vY0bV80sl4vggj73KZwh',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedTaskId: 'vY0bV80sl4vggj73KZwh',"}}
[info] >    selectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  selectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    currentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  currentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   currentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      deletionInfo: { action: 'delete', confidence: 95 }, {"user":"    deletionInfo: { action: 'delete', confidence: 95 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     deletionInfo: { action: 'delete', confidence: 95 },"}}
[info] >      farmId: '7owH6eTfGSYqaRpgIsQE', {"user":"    farmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     farmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      farmName: 'Haven_View', {"user":"    farmName: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     farmName: 'Haven_View',"}}
[info] >      needsTaskSelection: true {"user":"    needsTaskSelection: true","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsTaskSelection: true"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven_View', {"user":"      name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven_View',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'BtniJTurDAn0saHHbEGF', {"user":"      id: 'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'BtniJTurDAn0saHHbEGF',"}}
[info] >        name: 'Green Land..', {"user":"      name: 'Green Land..',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Green Land..',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'wFSF3LbHQwPYy6A0pu94', {"user":"      id: 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >        name: 'Dairy House ', {"user":"      name: 'Dairy House ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Dairy House ',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'fNgkVoyHni01zmozumbK', {"user":"      id: 'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'fNgkVoyHni01zmozumbK',"}}
[info] >        name: 'Octans Farm', {"user":"      name: 'Octans Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Octans Farm',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'oXnGAP4V78vilhIVpIQN', {"user":"      id: 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'oXnGAP4V78vilhIVpIQN',"}}
[info] >        name: 'Trade Farm', {"user":"      name: 'Trade Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Trade Farm',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'NpS3CdrpHvPZClMMDeFc', {"user":"      id: 'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'NpS3CdrpHvPZClMMDeFc',"}}
[info] >        name: 'River view ', {"user":"      name: 'River view ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'River view ',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'TlS4g9PWKN1wNBtjHEoq', {"user":"      id: 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >        name: 'Sabz Chara', {"user":"      name: 'Sabz Chara',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Sabz Chara',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'rb2n32HuXJlFOk44cAkJ', {"user":"      id: 'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'rb2n32HuXJlFOk44cAkJ',"}}
[info] >        name: 'Farm_002', {"user":"      name: 'Farm_002',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_002',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'UWkNKuXggt70Oe4bkzcx', {"user":"      id: 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx' {"user":"    'UWkNKuXggt70Oe4bkzcx'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Error processing task selection for deletion: Error: Cannot find module './services/database-service' {"user":"Error processing task selection for deletion: Error: Cannot find module './services/database-service'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Error processing task selection for deletion: Error: Cannot find module './services/database-service'"}}
[info] >  Require stack: {"user":"Require stack:","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Require stack:"}}
[info] >  - E:\python_projects_factory\animal\mcp-server\functions\controller\ai.js {"user":"- E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m - E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js"}}
[info] >  - E:\python_projects_factory\animal\mcp-server\functions\routes\openAi.js {"user":"- E:\\python_projects_factory\\animal\\mcp-server\\functions\\routes\\openAi.js","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m - E:\\python_projects_factory\\animal\\mcp-server\\functions\\routes\\openAi.js"}}
[info] >  - E:\python_projects_factory\animal\mcp-server\functions\index.js {"user":"- E:\\python_projects_factory\\animal\\mcp-server\\functions\\index.js","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m - E:\\python_projects_factory\\animal\\mcp-server\\functions\\index.js"}}
[info] >  - C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\emulator\functionsEmulatorRuntime.js {"user":"- C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\firebase-tools\\lib\\emulator\\functionsEmulatorRuntime.js","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m - C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\firebase-tools\\lib\\emulator\\functionsEmulatorRuntime.js"}}
[info] >      at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15) {"user":"    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)"}}
[info] >      at Function._load (node:internal/modules/cjs/loader:1055:27) {"user":"    at Function._load (node:internal/modules/cjs/loader:1055:27)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at Function._load (node:internal/modules/cjs/loader:1055:27)"}}
[info] >      at TracingChannel.traceSync (node:diagnostics_channel:322:14) {"user":"    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at TracingChannel.traceSync (node:diagnostics_channel:322:14)"}}
[info] >      at wrapModuleLoad (node:internal/modules/cjs/loader:220:24) {"user":"    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)"}}
[info] >      at Module.require (node:internal/modules/cjs/loader:1311:12) {"user":"    at Module.require (node:internal/modules/cjs/loader:1311:12)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at Module.require (node:internal/modules/cjs/loader:1311:12)"}}
[info] >      at require (node:internal/modules/helpers:136:16) {"user":"    at require (node:internal/modules/helpers:136:16)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at require (node:internal/modules/helpers:136:16)"}}
[info] >      at exports.openAiChat (E:\python_projects_factory\animal\mcp-server\functions\controller\ai.js:817:44) {"user":"    at exports.openAiChat (E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js:817:44)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at exports.openAiChat (E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js:817:44)"}}
[info] >      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) { {"user":"    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {"}}
[info] >    code: 'MODULE_NOT_FOUND', {"user":"  code: 'MODULE_NOT_FOUND',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   code: 'MODULE_NOT_FOUND',"}}
[info] >    requireStack: [ {"user":"  requireStack: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requireStack: ["}}
[info] >      'E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js', {"user":"    'E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\controller\\\\ai.js',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\controller\\\\ai.js',"}}
[info] >      'E:\\python_projects_factory\\animal\\mcp-server\\functions\\routes\\openAi.js', {"user":"    'E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\routes\\\\openAi.js',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\routes\\\\openAi.js',"}}
[info] >      'E:\\python_projects_factory\\animal\\mcp-server\\functions\\index.js', {"user":"    'E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\index.js',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\index.js',"}}
[info] >      'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\firebase-tools\\lib\\emulator\\functionsEmulatorRuntime.js' {"user":"    'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\firebase-tools\\\\lib\\\\emulator\\\\functionsEmulatorRuntime.js'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\npm\\\\node_modules\\\\firebase-tools\\\\lib\\\\emulator\\\\functionsEmulatorRuntime.js'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-29T10:56:17.892Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2465.1896ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2465.1896ms"}}
[debug] [2025-07-29T10:56:17.893Z] [worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-532a3135-aaf2-47bf-a350-25c507b581d5]: IDLE"}}
[debug] [2025-07-29T10:56:17.893Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T10:56:17.894Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T10:56:17.894Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'vY0bV80sl4vggj73KZwh', {"user":"  prompt: 'vY0bV80sl4vggj73KZwh',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'vY0bV80sl4vggj73KZwh',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextKeys: [ 'deletionInfo', 'farmId', 'farmName', 'needsTaskSelection' ], {"user":"  contextKeys: [ 'deletionInfo', 'farmId', 'farmName', 'needsTaskSelection' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [ 'deletionInfo', 'farmId', 'farmName', 'needsTaskSelection' ],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing task selection for deletion... {"user":"Processing task selection for deletion...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing task selection for deletion..."}}
[info] >  Task selection details: { {"user":"Task selection details: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Task selection details: {"}}
[info] >    selectedTaskId: 'vY0bV80sl4vggj73KZwh', {"user":"  selectedTaskId: 'vY0bV80sl4vggj73KZwh',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedTaskId: 'vY0bV80sl4vggj73KZwh',"}}
[info] >    farmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  farmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    farmName: 'Haven_View', {"user":"  farmName: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmName: 'Haven_View',"}}
[info] >    deletionInfo: { action: 'delete', confidence: 95 } {"user":"  deletionInfo: { action: 'delete', confidence: 95 }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   deletionInfo: { action: 'delete', confidence: 95 }"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
